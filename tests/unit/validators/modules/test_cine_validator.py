"""
Test CineValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.7.6.5 Cine Module validation.
"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.cine_validator import CineValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.image_enums import PreferredPlaybackSequencing, ChannelMode
from pyrt_dicom.modules.cine_module import CineModule


class TestCineValidator:
    """Test CineValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = CineValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_passes_validation(self):
        """Test that empty dataset passes validation (no Type 1 elements in Cine Module)."""
        dataset = Dataset()
        
        result = CineValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_frame_time_passes_validation(self):
        """Test that valid Frame Time passes validation without errors."""
        dataset = Dataset()
        dataset.FrameTime = 33.33  # 30 fps
        
        result = CineValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_frame_time_vector_passes_validation(self):
        """Test that valid Frame Time Vector passes validation without errors."""
        dataset = Dataset()
        # Use setattr to bypass pydicom validation
        setattr(dataset, 'FrameTimeVector', [0, 33.33, 33.33, 33.33])

        result = CineValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_negative_frame_time_generates_error(self):
        """Test that negative Frame Time generates validation error."""
        dataset = Dataset()
        dataset.FrameTime = -10.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Frame Time (0018,1063) must be positive" in result.errors[0]
    
    def test_frame_time_boundary_values(self):
        """Test Frame Time with boundary values."""
        # Test very small positive value
        dataset = Dataset()
        dataset.FrameTime = 0.001  # Very small but valid

        result = CineValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors

    def test_frame_time_vector_single_element(self):
        """Test Frame Time Vector with single element."""
        dataset = Dataset()
        # Use setattr to set single element vector
        setattr(dataset, 'FrameTimeVector', [0])

        result = CineValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_errors
    
    def test_frame_time_vector_with_non_zero_first_value_generates_warning(self):
        """Test that Frame Time Vector with non-zero first value generates warning."""
        dataset = Dataset()
        # Use setattr to bypass pydicom validation
        setattr(dataset, 'FrameTimeVector', [10.0, 33.33, 33.33])  # First should be 0

        result = CineValidator.validate(dataset)

        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert len(result.warnings) == 1
        assert "Frame Time Vector (0018,1065) first value should be 0" in result.warnings[0]
    
    def test_frame_time_vector_with_negative_values_generates_error(self):
        """Test that Frame Time Vector with negative values generates validation error."""
        dataset = Dataset()
        # Use setattr to bypass pydicom validation
        setattr(dataset, 'FrameTimeVector', [0, 33.33, -10.0, 33.33])

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Frame Time Vector (0018,1065) values must be non-negative" in result.errors[0]
    
    def test_both_frame_time_and_vector_present_generates_warning(self):
        """Test that having both Frame Time and Frame Time Vector generates warning."""
        dataset = Dataset()
        dataset.FrameTime = 33.33
        # Use setattr to bypass pydicom validation
        setattr(dataset, 'FrameTimeVector', [0, 33.33, 33.33])

        result = CineValidator.validate(dataset)

        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert len(result.warnings) == 1
        assert "Both Frame Time (0018,1063) and Frame Time Vector (0018,1065) are present" in result.warnings[0]
    
    def test_valid_preferred_playback_sequencing_passes_validation(self):
        """Test that valid Preferred Playback Sequencing passes validation."""
        dataset = Dataset()
        dataset.PreferredPlaybackSequencing = PreferredPlaybackSequencing.LOOPING.value
        
        config = ValidationConfig(check_enumerated_values=True)
        result = CineValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_invalid_preferred_playback_sequencing_generates_warning(self):
        """Test that invalid Preferred Playback Sequencing generates validation warning."""
        dataset = Dataset()
        # Use setattr to bypass pydicom validation
        setattr(dataset, 'PreferredPlaybackSequencing', "INVALID_VALUE")

        config = ValidationConfig(check_enumerated_values=True)
        result = CineValidator.validate(dataset, config)

        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        # Should have both enumerated value warning and limitation warning
        playback_warning = any("Preferred Playback Sequencing (0018,1244)" in warning and 
                              "INVALID_VALUE" in warning for warning in result.warnings)
        assert playback_warning, "Should have warning about invalid playback sequencing value"
    
    def test_valid_audio_channels_sequence_passes_validation(self):
        """Test that valid audio channels sequence passes validation."""
        dataset = Dataset()
        
        # Create valid audio channel item
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_audio_channel_missing_identification_code_generates_error(self):
        """Test that audio channel missing identification code generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        # Missing ChannelIdentificationCode
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Identification Code (003A,0301) is required" in error for error in result.errors)
    
    def test_audio_channel_invalid_identification_code_generates_warning(self):
        """Test that audio channel with invalid identification code generates warning."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 15  # Should be 1-9
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert any("Channel Identification Code should be 1-9" in warning for warning in result.warnings)
    
    def test_audio_channel_missing_mode_generates_error(self):
        """Test that audio channel missing mode generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        # Missing ChannelMode
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Mode (003A,0302) is required" in error for error in result.errors)
    
    def test_audio_channel_invalid_mode_generates_error(self):
        """Test that audio channel with invalid mode generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = "INVALID_MODE"
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Mode (003A,0302) has invalid value 'INVALID_MODE'" in error for error in result.errors)
    
    def test_audio_channel_missing_source_sequence_generates_error(self):
        """Test that audio channel missing source sequence generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value
        # Missing ChannelSourceSequence
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Source Sequence (003A,0208) is required" in error for error in result.errors)
    
    def test_audio_channel_invalid_source_sequence_count_generates_error(self):
        """Test that audio channel with invalid source sequence count generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        # Multiple source items (should be exactly one)
        source_item1 = Dataset()
        source_item1.CodeValue = "MAIN"
        source_item1.CodingSchemeDesignator = "DCM"
        
        source_item2 = Dataset()
        source_item2.CodeValue = "AUX"
        source_item2.CodingSchemeDesignator = "DCM"
        
        channel_item.ChannelSourceSequence = [source_item1, source_item2]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Source Sequence (003A,0208) must contain exactly one item" in error for error in result.errors)
    
    def test_audio_channel_source_missing_code_value_generates_error(self):
        """Test that audio channel source missing code value generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        # Missing CodeValue
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Source Sequence item missing Code Value" in error for error in result.errors)
    
    def test_audio_channel_source_missing_coding_scheme_generates_error(self):
        """Test that audio channel source missing coding scheme generates error."""
        dataset = Dataset()
        
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        # Missing CodingSchemeDesignator
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Channel Source Sequence item missing Coding Scheme Designator" in error for error in result.errors)

    def test_invalid_start_trim_generates_error(self):
        """Test that invalid Start Trim generates validation error."""
        dataset = Dataset()
        dataset.StartTrim = 0  # Should be >= 1 (frame numbers are 1-based)
        dataset.StopTrim = 10

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("Start Trim (0008,2142) must be >= 1" in error for error in result.errors)

    def test_invalid_stop_trim_generates_error(self):
        """Test that invalid Stop Trim generates validation error."""
        dataset = Dataset()
        dataset.StartTrim = 5
        dataset.StopTrim = 0  # Should be >= 1 (frame numbers are 1-based)

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("Stop Trim (0008,2143) must be >= 1" in error for error in result.errors)

    def test_start_trim_greater_than_stop_trim_generates_error(self):
        """Test that Start Trim greater than Stop Trim generates validation error."""
        dataset = Dataset()
        dataset.StartTrim = 10
        dataset.StopTrim = 5

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("Start Trim (10) cannot be greater than Stop Trim (5)" in error for error in result.errors)

    def test_zero_trim_values_generate_error(self):
        """Test that zero trim values generate validation error."""
        dataset = Dataset()
        dataset.StartTrim = 0  # Invalid: should be >= 1
        dataset.StopTrim = 0   # Invalid: should be >= 1

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("Start Trim (0008,2142) must be >= 1" in error for error in result.errors)
        assert any("Stop Trim (0008,2143) must be >= 1" in error for error in result.errors)

    def test_negative_frame_rate_generates_error(self):
        """Test that negative frame rate generates validation error."""
        dataset = Dataset()
        dataset.RecommendedDisplayFrameRate = -5.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("RecommendedDisplayFrameRate (0008,2144) must be positive" in error for error in result.errors)

    def test_zero_cine_rate_generates_error(self):
        """Test that zero cine rate generates validation error."""
        dataset = Dataset()
        dataset.CineRate = 0.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("CineRate (0018,0040) must be positive" in error for error in result.errors)

    def test_unusually_high_frame_rate_generates_warning(self):
        """Test that unusually high frame rate generates validation warning."""
        dataset = Dataset()
        dataset.RecommendedDisplayFrameRate = 2000.0  # Very high frame rate

        result = CineValidator.validate(dataset)

        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert any("seems unusually high for frame rate" in warning for warning in result.warnings)

    def test_very_high_frame_rate_generates_warning(self):
        """Test that very high frame rate generates validation warning."""
        dataset = Dataset()
        dataset.RecommendedDisplayFrameRate = 5000.0  # Extremely high

        result = CineValidator.validate(dataset)

        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert any("seems unusually high for frame rate" in warning for warning in result.warnings)

    def test_negative_duration_generates_error(self):
        """Test that negative duration generates validation error."""
        dataset = Dataset()
        dataset.FrameDelay = -10.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("FrameDelay (0018,1066) must be non-negative" in error for error in result.errors)

    def test_negative_image_trigger_delay_generates_error(self):
        """Test that negative image trigger delay generates validation error."""
        dataset = Dataset()
        dataset.ImageTriggerDelay = -5.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("ImageTriggerDelay (0018,1067) must be non-negative" in error for error in result.errors)

    def test_negative_effective_duration_generates_error(self):
        """Test that negative effective duration generates validation error."""
        dataset = Dataset()
        dataset.EffectiveDuration = -1.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("EffectiveDuration (0018,0072) must be non-negative" in error for error in result.errors)

    def test_negative_actual_frame_duration_generates_error(self):
        """Test that negative actual frame duration generates validation error."""
        dataset = Dataset()
        dataset.ActualFrameDuration = -20.0

        result = CineValidator.validate(dataset)

        assert not result.is_valid
        assert result.has_errors
        assert any("ActualFrameDuration (0018,1242) must be non-negative" in error for error in result.errors)

    def test_very_long_duration_generates_warning(self):
        """Test that very long duration generates validation warning."""
        dataset = Dataset()
        dataset.EffectiveDuration = 3600.0  # 1 hour - very long for cine

        result = CineValidator.validate(dataset)

        assert result.is_valid  # Should be valid, just unusual
        # Note: This test verifies the validator handles edge cases gracefully

    def test_inconsistent_frame_time_and_cine_rate_generates_warning(self):
        """Test that inconsistent Frame Time and Cine Rate generates warning."""
        dataset = Dataset()
        dataset.FrameTime = 33.33  # ~30 fps
        dataset.CineRate = 60.0    # 60 fps - inconsistent

        result = CineValidator.validate(dataset)

        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert any("Frame Time (33.33 msec) and Cine Rate (60.0 fps) are inconsistent" in warning for warning in result.warnings)

    def test_consistent_frame_time_and_cine_rate_passes_validation(self):
        """Test that consistent Frame Time and Cine Rate passes validation."""
        dataset = Dataset()
        dataset.FrameTime = 33.33  # ~30 fps
        dataset.CineRate = 30.0    # 30 fps - consistent

        result = CineValidator.validate(dataset)

        assert result.is_valid
        assert not result.has_warnings
        assert len(result.warnings) == 0

    def test_validation_with_default_config(self):
        """Test validation behavior with default ValidationConfig."""
        dataset = Dataset()
        dataset.FrameTime = 33.33
        dataset.PreferredPlaybackSequencing = PreferredPlaybackSequencing.LOOPING.value

        # Test with None config (should use defaults)
        result = CineValidator.validate(dataset, None)

        assert isinstance(result, ValidationResult)
        assert result.is_valid

    def test_validation_with_custom_config(self):
        """Test validation behavior with custom ValidationConfig."""
        dataset = Dataset()
        dataset.PreferredPlaybackSequencing = "INVALID_VALUE"

        # Test with enumerated value checking disabled
        config = ValidationConfig(check_enumerated_values=False)
        result = CineValidator.validate(dataset, config)

        assert result.is_valid  # Should not check enumerated values
        # May have limitation warning since conditional validation is enabled by default
        if result.has_warnings:
            # Should not have enumerated value warnings
            enum_warning = any("Preferred Playback Sequencing" in warning for warning in result.warnings)
            assert not enum_warning, "Should not have enumerated value warnings when disabled"

    def test_validation_with_sequences_disabled(self):
        """Test validation behavior with sequence validation disabled."""
        dataset = Dataset()

        # Create invalid audio channel (missing required fields)
        channel_item = Dataset()
        # Missing all required fields
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]

        config = ValidationConfig(validate_sequences=False)
        result = CineValidator.validate(dataset, config)

        assert result.is_valid  # Should not validate sequences
        assert not result.has_errors

    def test_validation_with_conditional_requirements_disabled(self):
        """Test validation behavior with conditional requirements disabled."""
        dataset = Dataset()
        dataset.FrameTime = 33.33
        dataset.FrameTimeVector = [0, 33.33, 33.33]  # Both present - should warn

        config = ValidationConfig(validate_conditional_requirements=False)
        result = CineValidator.validate(dataset, config)

        assert result.is_valid  # Should not check conditional requirements
        assert not result.has_warnings

    def test_error_message_includes_dicom_tag_references(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        dataset.FrameTime = -10.0  # Invalid

        result = CineValidator.validate(dataset)

        assert result.has_errors
        assert any("(0018,1063)" in error for error in result.errors)

    def test_error_messages_are_human_readable(self):
        """Test that error messages are clear and actionable for end users."""
        dataset = Dataset()
        dataset.StartTrim = 10
        dataset.StopTrim = 5

        result = CineValidator.validate(dataset)

        assert result.has_errors
        error_message = result.errors[0]
        assert "Start Trim" in error_message
        assert "Stop Trim" in error_message
        assert "cannot be greater than" in error_message
        assert "10" in error_message and "5" in error_message

    def test_multiple_validation_issues_are_all_reported(self):
        """Test that multiple validation issues are all captured and reported."""
        dataset = Dataset()
        dataset.FrameTime = -10.0  # Error: negative
        dataset.PreferredPlaybackSequencing = "INVALID"  # Warning: invalid enum
        dataset.StartTrim = 10
        dataset.StopTrim = 5  # Error: start > stop

        config = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True
        )
        result = CineValidator.validate(dataset, config)

        # Should have multiple issues reported
        assert result.has_errors or result.has_warnings
        assert result.total_issues >= 3  # At least 2 errors + 1 warning

    def test_validation_result_consistency_across_scenarios(self):
        """Test that ValidationResult format is consistent across all validation scenarios."""
        test_datasets = [
            Dataset(),  # Empty dataset
            self._create_valid_dataset(),
            self._create_invalid_dataset()
        ]

        for dataset in test_datasets:
            result = CineValidator.validate(dataset)

            # Verify consistent ValidationResult structure
            assert isinstance(result, ValidationResult)
            assert hasattr(result, 'errors')
            assert hasattr(result, 'warnings')
            assert hasattr(result, 'is_valid')
            assert hasattr(result, 'has_errors')
            assert hasattr(result, 'has_warnings')
            assert isinstance(result.errors, list)
            assert isinstance(result.warnings, list)

    def test_validation_performance_with_large_datasets(self):
        """Test that validation completes within reasonable time bounds."""
        import time

        dataset = Dataset()
        dataset.FrameTime = 33.33

        # Create large audio channel sequence
        audio_channels = []
        for i in range(100):  # Large number of channels
            channel_item = Dataset()
            channel_item.ChannelIdentificationCode = (i % 9) + 1
            channel_item.ChannelMode = ChannelMode.MONO.value

            source_item = Dataset()
            source_item.CodeValue = f"CHANNEL_{i}"
            source_item.CodingSchemeDesignator = "DCM"
            channel_item.ChannelSourceSequence = [source_item]

            audio_channels.append(channel_item)

        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = audio_channels

        start_time = time.time()
        result = CineValidator.validate(dataset)
        end_time = time.time()

        # Should complete within reasonable time (< 1 second for 100 channels)
        assert (end_time - start_time) < 1.0
        assert isinstance(result, ValidationResult)

    def test_validation_handles_edge_cases_gracefully(self):
        """Test that validators handle edge cases gracefully without crashes."""
        dataset = Dataset()

        # Test with extreme but valid values
        dataset.FrameTime = 0.001  # Very small but positive
        dataset.RecommendedDisplayFrameRate = 0.1  # Very low frame rate

        # Should not crash, should return validation result
        result = CineValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        # Very small frame time should be valid (positive)

    # Helper methods for test data creation
    def _create_valid_dataset(self) -> Dataset:
        """Create a valid cine dataset for testing."""
        dataset = Dataset()
        dataset.FrameTime = 33.33
        dataset.PreferredPlaybackSequencing = PreferredPlaybackSequencing.LOOPING.value
        dataset.RecommendedDisplayFrameRate = 30.0
        dataset.CineRate = 30.0
        dataset.StartTrim = 1
        dataset.StopTrim = 100
        return dataset

    def _create_invalid_dataset(self) -> Dataset:
        """Create an invalid cine dataset for testing."""
        dataset = Dataset()
        dataset.FrameTime = -10.0  # Invalid: negative
        dataset.PreferredPlaybackSequencing = "INVALID"  # Invalid: not in enum
        dataset.StartTrim = 10
        dataset.StopTrim = 5  # Invalid: start > stop
        return dataset

    # Documentation tests for architectural limitations
    def test_type_1c_validation_limitation_documented(self):
        """Test that Type 1C validation limitation is documented for end users."""
        dataset = Dataset()
        # Add a cine attribute but no frame time elements to trigger limitation warning
        dataset.CineRate = 30.0
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = CineValidator.validate(dataset, config)
        
        # Should document the limitation about Frame Increment Pointer
        assert result.has_warnings
        limitation_warning = any("VALIDATION LIMITATION" in warning and 
                               "Frame Increment Pointer" in warning and
                               "Multi-frame Module" in warning 
                               for warning in result.warnings)
        assert limitation_warning, "Should document Type 1C validation limitation"

    def test_cross_module_dependency_challenge_documented(self):
        """Test that cross-module dependency challenge is clearly documented."""
        dataset = Dataset()
        dataset.FrameTime = 33.33
        dataset.FrameTimeVector = [0, 33.33, 33.33]  # Both present - cross-module issue
        
        result = CineValidator.validate(dataset)
        
        # Should warn about cross-module dependency
        assert result.has_warnings
        cross_module_warning = any("cross-module dependency" in warning.lower() or
                                 "multi-frame module" in warning.lower()
                                 for warning in result.warnings)
        assert cross_module_warning, "Should document cross-module dependency challenge"

    def test_transfer_syntax_conditional_limitation_example(self):
        """Test example showing Transfer Syntax conditional validation limitation."""
        dataset = Dataset()
        
        # Audio channels present but we can't validate if transfer syntax requires them
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]
        
        result = CineValidator.validate(dataset)
        
        # Should pass validation but note that we can't validate Type 2C requirement
        # without knowing the transfer syntax
        assert result.is_valid  # Structurally valid
        # Note: In full implementation, this would document transfer syntax limitation
    
    def test_audio_channel_invalid_structure_validation(self):
        """Test that audio channel items with invalid structure generate validation errors."""
        dataset = Dataset()
        
        # Create invalid audio channel item (Dataset but missing required attributes)
        invalid_channel = Dataset()
        invalid_channel.ChannelIdentificationCode = 1
        # Missing required ChannelMode and ChannelSourceSequence
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [invalid_channel]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors about missing required attributes
        assert any("Channel Mode (003A,0302) is required" in error for error in result.errors)
        assert any("Channel Source Sequence (003A,0208) is required" in error for error in result.errors)
    
    def test_validator_handles_moved_validation_logic(self):
        """Test that validator properly handles validation logic moved from module."""
        dataset = Dataset()
        
        # Test frame time vector with non-zero first value (moved from module)
        setattr(dataset, 'FrameTimeVector', [10.0, 33.33, 33.33])  # First should be 0
        
        result = CineValidator.validate(dataset)
        
        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert any("Frame Time Vector (0018,1065) first value should be 0" in warning 
                  for warning in result.warnings)
    
    def test_validator_audio_channel_structure_validation(self):
        """Test comprehensive audio channel structure validation in validator."""
        dataset = Dataset()
        
        # Create mixed valid/invalid audio channel items
        valid_channel = Dataset()
        valid_channel.ChannelIdentificationCode = 1
        valid_channel.ChannelMode = ChannelMode.MONO.value
        
        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        valid_channel.ChannelSourceSequence = [source_item]
        
        # Invalid channel (Dataset but missing required attributes)
        invalid_channel = Dataset()
        invalid_channel.ChannelIdentificationCode = 2
        # Missing ChannelMode and ChannelSourceSequence
        
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [valid_channel, invalid_channel]
        
        config = ValidationConfig(validate_sequences=True)
        result = CineValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors about missing required attributes in second item
        assert any("Audio channel item 2: Channel Mode (003A,0302) is required" in error 
                  for error in result.errors)
        assert any("Audio channel item 2: Channel Source Sequence (003A,0208) is required" in error
                  for error in result.errors)


class TestCineValidatorGranularMethods:
    """Test individual granular validation methods with both Dataset and BaseModule."""

    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements with Dataset instance."""
        dataset = Dataset()
        dataset.FrameTime = 33.33

        result = CineValidator.validate_required_elements(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No Type 1/2 elements in Cine Module
        assert len(result.errors) == 0

    def test_validate_required_elements_with_basemodule(self):
        """Test validate_required_elements with BaseModule instance."""
        cine = CineModule.from_required_elements().with_frame_time(33.33)

        result = CineValidator.validate_required_elements(cine)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors  # No Type 1/2 elements in Cine Module
        assert len(result.errors) == 0

    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements with Dataset instance."""
        dataset = Dataset()
        dataset.FrameTime = 33.33

        result = CineValidator.validate_conditional_requirements(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid frame time
        dataset.FrameTime = -10.0
        result = CineValidator.validate_conditional_requirements(dataset)
        assert result.has_errors
        assert any("Frame Time" in error and "positive" in error for error in result.errors)

    def test_validate_conditional_requirements_with_basemodule(self):
        """Test validate_conditional_requirements with BaseModule instance."""
        cine = CineModule.from_required_elements().with_frame_time(33.33)

        result = CineValidator.validate_conditional_requirements(cine)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid frame time
        cine._dataset.FrameTime = -10.0
        result = CineValidator.validate_conditional_requirements(cine)
        assert result.has_errors
        assert any("Frame Time" in error and "positive" in error for error in result.errors)

    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values with Dataset instance."""
        dataset = Dataset()
        dataset.PreferredPlaybackSequencing = PreferredPlaybackSequencing.LOOPING.value

        result = CineValidator.validate_enumerated_values(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid enum value
        dataset.PreferredPlaybackSequencing = 999
        result = CineValidator.validate_enumerated_values(dataset)
        assert result.has_warnings  # Enum validation typically generates warnings

    def test_validate_enumerated_values_with_basemodule(self):
        """Test validate_enumerated_values with BaseModule instance."""
        cine = CineModule.from_required_elements()
        cine.with_optional_elements(preferred_playback_sequencing=PreferredPlaybackSequencing.LOOPING)

        result = CineValidator.validate_enumerated_values(cine)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid enum value
        cine._dataset.PreferredPlaybackSequencing = 999
        result = CineValidator.validate_enumerated_values(cine)
        assert result.has_warnings  # Enum validation typically generates warnings

    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures with Dataset instance."""
        dataset = Dataset()

        # Create valid audio channel
        channel_item = Dataset()
        channel_item.ChannelIdentificationCode = 1
        channel_item.ChannelMode = ChannelMode.MONO.value

        source_item = Dataset()
        source_item.CodeValue = "MAIN"
        source_item.CodingSchemeDesignator = "DCM"
        channel_item.ChannelSourceSequence = [source_item]

        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel_item]

        result = CineValidator.validate_sequence_structures(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid sequence structure (create mock dataset)
        class MockDataset:
            def __init__(self):
                self.MultiplexedAudioChannelsDescriptionCodeSequence = "not_a_sequence"

            def __contains__(self, key):
                return hasattr(self, key)

            def __getattr__(self, name):
                if hasattr(self, name):
                    return getattr(self, name)
                raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

        mock_dataset = MockDataset()
        result = CineValidator.validate_sequence_structures(mock_dataset)
        assert result.has_errors
        assert any("must be a sequence" in error for error in result.errors)

    def test_validate_sequence_structures_with_basemodule(self):
        """Test validate_sequence_structures with BaseModule instance."""
        cine = CineModule.from_required_elements()

        # Create valid audio channel
        audio_channel = CineModule.create_audio_channel_item(
            channel_id=1,
            channel_mode=ChannelMode.MONO,
            source_code_value='MAIN',
            source_coding_scheme='DCM'
        )
        cine.with_audio_channels([audio_channel], transfer_syntax_has_audio=True)

        result = CineValidator.validate_sequence_structures(cine)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with invalid sequence structure (create mock module)
        class MockModule:
            def __init__(self):
                self.MultiplexedAudioChannelsDescriptionCodeSequence = "not_a_sequence"

            def __contains__(self, key):
                return hasattr(self, key)

            def __getattr__(self, name):
                if hasattr(self, name):
                    return getattr(self, name)
                raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")

        mock_module = MockModule()
        result = CineValidator.validate_sequence_structures(mock_module)
        assert result.has_errors
        assert any("must be a sequence" in error for error in result.errors)

    def test_validate_timing_consistency_with_dataset(self):
        """Test validate_timing_consistency with Dataset instance."""
        dataset = Dataset()
        dataset.FrameTime = 33.33
        dataset.CineRate = 30.0
        dataset.StartTrim = 1
        dataset.StopTrim = 100

        result = CineValidator.validate_timing_consistency(dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with inconsistent timing
        dataset.StartTrim = 10
        dataset.StopTrim = 5
        result = CineValidator.validate_timing_consistency(dataset)
        assert result.has_errors
        assert any("Start Trim" in error and "Stop Trim" in error for error in result.errors)

    def test_validate_timing_consistency_with_basemodule(self):
        """Test validate_timing_consistency with BaseModule instance."""
        cine = CineModule.from_required_elements().with_frame_time(33.33)
        cine.with_optional_elements(cine_rate=30.0, start_trim=1, stop_trim=100)

        result = CineValidator.validate_timing_consistency(cine)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors

        # Test with inconsistent timing
        cine._dataset.StartTrim = 10
        cine._dataset.StopTrim = 5
        result = CineValidator.validate_timing_consistency(cine)
        assert result.has_errors
        assert any("Start Trim" in error and "Stop Trim" in error for error in result.errors)


class TestCineValidatorIndependence:
    """Test validator independence - works with external Dataset instances."""

    def test_validator_works_with_external_dataset(self):
        """Test that validator works with external pydicom Dataset instances."""
        # Create external dataset (not from CineModule)
        external_dataset = Dataset()
        external_dataset.FrameTime = 33.33
        external_dataset.PreferredPlaybackSequencing = 0
        external_dataset.CineRate = 30.0

        # Validator should work independently
        result = CineValidator.validate(external_dataset)

        assert isinstance(result, ValidationResult)
        assert not result.has_errors
        assert result.is_valid

    def test_validator_independence_from_module_classes(self):
        """Test that validator has no dependency on module classes."""
        # Create dataset manually without using CineModule
        dataset = Dataset()
        dataset.FrameTime = -10.0  # Invalid
        dataset.PreferredPlaybackSequencing = 999  # Invalid

        # Validator should validate independently
        result = CineValidator.validate(dataset)

        assert isinstance(result, ValidationResult)
        assert result.has_errors
        assert any("Frame Time" in error for error in result.errors)

    def test_granular_methods_work_independently(self):
        """Test that granular validation methods work independently."""
        dataset = Dataset()
        dataset.FrameTime = -5.0
        dataset.PreferredPlaybackSequencing = 999

        # Test each granular method independently
        result1 = CineValidator.validate_conditional_requirements(dataset)
        assert result1.has_errors

        result2 = CineValidator.validate_enumerated_values(dataset)
        assert result2.has_warnings

        result3 = CineValidator.validate_required_elements(dataset)
        assert not result3.has_errors  # No required elements

        result4 = CineValidator.validate_sequence_structures(dataset)
        assert not result4.has_errors  # No sequences present

        result5 = CineValidator.validate_timing_consistency(dataset)
        assert not result5.has_errors  # No timing attributes to check


class TestCineValidatorComplexScenarios:
    """Test complex conditional validation scenarios."""

    def test_complex_type_1c_frame_increment_scenarios(self):
        """Test complex Type 1C scenarios with frame increment dependencies."""
        # Test scenario 1: Neither frame time nor vector present
        dataset = Dataset()
        dataset.CineRate = 30.0  # Add cine-related attribute

        result = CineValidator.validate_conditional_requirements(dataset)
        assert result.has_warnings
        assert any("VALIDATION LIMITATION" in warning for warning in result.warnings)

        # Test scenario 2: Both present (architectural issue)
        dataset.FrameTime = 33.33
        setattr(dataset, 'FrameTimeVector', [0, 33.33, 33.33])

        result = CineValidator.validate_conditional_requirements(dataset)
        assert result.has_warnings
        assert any("Both Frame Time" in warning for warning in result.warnings)

        # Test scenario 3: Frame Time Vector with invalid first value
        dataset = Dataset()
        setattr(dataset, 'FrameTimeVector', [10.0, 33.33, 33.33])  # First should be 0

        result = CineValidator.validate_conditional_requirements(dataset)
        assert result.has_warnings
        assert any("first value should be 0" in warning for warning in result.warnings)

    def test_complex_type_2c_audio_scenarios(self):
        """Test complex Type 2C audio sequence scenarios."""
        # Test scenario 1: Audio sequence present but empty (valid)
        dataset = Dataset()
        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = []

        result = CineValidator.validate_sequence_structures(dataset)
        assert not result.has_errors  # Empty sequence is valid

        # Test scenario 2: Multiple audio channels with mixed validity
        channel1 = Dataset()
        channel1.ChannelIdentificationCode = 1
        channel1.ChannelMode = ChannelMode.MONO.value
        source1 = Dataset()
        source1.CodeValue = "MAIN"
        source1.CodingSchemeDesignator = "DCM"
        channel1.ChannelSourceSequence = [source1]

        channel2 = Dataset()
        channel2.ChannelIdentificationCode = 2
        # Missing ChannelMode and ChannelSourceSequence

        dataset.MultiplexedAudioChannelsDescriptionCodeSequence = [channel1, channel2]

        result = CineValidator.validate_sequence_structures(dataset)
        assert result.has_errors
        assert any("Audio channel item 2" in error for error in result.errors)

    def test_cross_validation_timing_scenarios(self):
        """Test complex cross-validation timing scenarios."""
        # Test scenario 1: Frame time and rate consistency
        dataset = Dataset()
        dataset.FrameTime = 33.33  # ~30 fps
        dataset.CineRate = 60.0    # Inconsistent

        result = CineValidator.validate_timing_consistency(dataset)
        assert result.has_warnings
        assert any("inconsistent" in warning.lower() for warning in result.warnings)

        # Test scenario 2: Multiple timing attributes with edge cases
        dataset = Dataset()
        dataset.FrameTime = 0.001  # Very small but valid
        dataset.CineRate = 1001.0  # Consistent but very high (> 1000 threshold)
        dataset.FrameDelay = 0.0   # Valid boundary value
        dataset.EffectiveDuration = 0.0  # Valid boundary value

        result = CineValidator.validate_timing_consistency(dataset)
        assert result.has_warnings  # High frame rate warning
        assert any("unusually high" in warning for warning in result.warnings)
